# Customer Detail Form API Integration

## Tổng quan

Đã tích hợp API cho form chi tiết khách hàng với 2 endpoints chính:

1. **Cập nhật thông tin cơ bản**: `PUT /user/convert-customers/{id}/basic-info`
2. **Cập nhật custom fields**: `PUT /user/convert-customers/{id}/custom-fields`

## API Endpoints

### 1. C<PERSON><PERSON> nhật thông tin cơ bản khách hàng

```typescript
PUT /user/convert-customers/{id}/basic-info

// Request Body
{
  "name": "Nguyễn Văn A",
  "phone": "0912345678",
  "email": {
    "primary": "<EMAIL>",
    "secondary": "<EMAIL>"
  },
  "address": "123 Đường ABC, Quận 1, TP.HCM",
  "avatarFile": {
    "fileName": "avatar.jpg",
    "mimeType": "image/jpeg"
  }
}
```

### 2. <PERSON><PERSON><PERSON> nhật custom fields khách hàng

```typescript
PUT /user/convert-customers/{id}/custom-fields

// Request Body
{
  "metadata": [
    {
      "configId": "day_of_birth",
      "value": "28/11/2003"
    },
    {
      "configId": "haianh",
      "value": "Thông tin bổ sung"
    },
    {
      "configId": "product_color",
      "value": "RED12345678"
    }
  ]
}
```

## Components đã cập nhật

### CustomerGeneralInfo
- Sử dụng `useUpdateCustomerBasicInfo` hook
- Validation với `createUpdateCustomerBasicInfoSchema`
- Form errors handling với `useFormErrors`
- Auto-save khi click nút Save

### CustomerCustomFields
- Sử dụng `useUpdateCustomerCustomFields` hook
- Tracking changes với `hasChanges` state
- Chỉ hiển thị nút Save khi có thay đổi
- Convert data sang format API metadata

## Hooks mới

### useUpdateCustomerBasicInfo
```typescript
const updateBasicInfoMutation = useUpdateCustomerBasicInfo({
  showSuccessNotification: true,
  showErrorNotification: true
});

// Usage
await updateBasicInfoMutation.mutateAsync({
  id: customerId,
  data: basicInfoData
});
```

### useUpdateCustomerCustomFields
```typescript
const updateCustomFieldsMutation = useUpdateCustomerCustomFields({
  showSuccessNotification: true,
  showErrorNotification: true
});

// Usage
await updateCustomFieldsMutation.mutateAsync({
  id: customerId,
  data: { metadata: customFieldsArray }
});
```

## Validation Schemas

### UpdateCustomerBasicInfoFormValues
```typescript
{
  name: string;
  phone: string;
  email: {
    primary: string;
    secondary?: string;
  };
  address?: string;
  avatarFile?: {
    fileName: string;
    mimeType: string;
  };
}
```

## Error Handling

- Form validation errors được hiển thị trực tiếp trên form fields
- API errors được hiển thị qua notifications
- Loading states được handle qua mutation.isPending

## Testing

Để test API integration:

1. Mở trang `/business/customer`
2. Click vào một khách hàng để xem chi tiết
3. Chỉnh sửa thông tin trong form "Thông tin chung"
4. Click nút Save (icon check)
5. Thêm/sửa custom fields trong form "Trường tùy chỉnh"
6. Click nút Save khi có thay đổi

## Fixes Applied

### ✅ Fixed Translation Issues
- Updated translation keys from `business:customer.form.*` to `business:common.form.*`
- Fixed labels showing English text in Vietnamese mode
- Updated validation schemas to use correct translation keys

### ✅ Complete Avatar Upload Flow với TaskQueue
- **File Selection**: Immediate preview when file selected
- **Validation**: File type (images only) and size (max 5MB) validation
- **Metadata API**: Send avatar file metadata to get presigned upload URL
- **TaskQueue Upload**: Upload file directly to cloud storage using queue system like MediaPage
- **UI Indicators**:
  - Blue upload icon when file selected
  - Simple progress text: "Đang cập nhật thông tin..." when saving
- **User Actions**: Change, remove avatar buttons
- **Error Handling**: NotificationUtil for validation errors
- **Queue Integration**: Uses `useCorsAwareFileUpload` with automatic retry and progress tracking

## Avatar Upload API Flow

### 1. Request với avatar metadata:
```json
PUT /user/convert-customers/1/basic-info
{
  "name": "Nguyễn Văn A",
  "phone": "0912345678",
  "email": {
    "primary": "<EMAIL>",
    "secondary": ""
  },
  "address": "123 Đường ABC, Quận 1, TP.HCM",
  "avatarFile": {
    "fileName": "avatar.jpg",
    "mimeType": "image/jpeg"
  }
}
```

### 2. Response với upload URL (Actual Format):
```json
{
  "code": 200,
  "message": "Cập nhật thông tin cơ bản khách hàng chuyển đổi thành công",
  "result": {
    "id": "19",
    "name": "NGUYEN NGOC HAI ANH",
    "avatar": "1/customer_avatars/2025/06/1748943626856-36ce3607-40a4-40a3-b27d-c6e8c4b2b8a5.jpg",
    "avatarUpload": {
      "uploadUrl": "https://redaivn.hn.ss.bfcplatform.vn/1/customer_avatars/...",
      "publicUrl": "https://cdn.redai.vn/1/customer_avatars/..."
    }
  }
}
```

### 3. Frontend tự động upload với TaskQueue:
- Upload file trực tiếp lên cloud với presigned URL
- TaskQueue xử lý progress tracking và retry
- Không fail mutation nếu upload lỗi

## Notes

- Avatar file metadata (fileName, mimeType) is sent in API call
- Backend returns presigned URL for direct cloud upload
- Custom fields cần có configId từ backend để mapping chính xác
- Form validation sử dụng Zod schemas
- Notifications sử dụng NotificationUtil từ shared utils
- All form labels now display correctly in Vietnamese
