# Avatar Upload Flow - Customer Basic Info

## Tổng quan

Đã implement flow upload avatar hoàn chỉnh với presigned URL từ backend để upload lên cloud storage.

## Flow hoạt động

### 1. User chọn avatar file
```typescript
// User click "Thay đổi" → chọn file
handleAvatarUpload(event) {
  const file = event.target.files[0];
  
  // Validation
  if (!file.type.startsWith('image/')) return;
  if (file.size > 5MB) return;
  
  // Create preview
  const previewUrl = URL.createObjectURL(file);
  setFormData(prev => ({ ...prev, avatar: previewUrl }));
  
  // Store file for later upload
  setAvatarFile(file);
}
```

### 2. User click Save
```typescript
handleSave() {
  // Prepare data with avatar file metadata
  const validatedData = {
    name: "Nguyễn Văn A",
    phone: "0912345678",
    email: { primary: "<EMAIL>", secondary: "" },
    address: "123 ABC Street",
    avatarFile: {
      fileName: "avatar.jpg",
      mimeType: "image/jpeg"
    }
  };
  
  // Call mutation with actual file
  updateBasicInfoMutation.mutateAsync({
    id: customerId,
    data: validatedData,
    avatarFile: actualFileObject
  });
}
```

### 3. API Call với avatar metadata
```bash
PUT /user/convert-customers/1/basic-info
{
  "name": "Nguyễn Văn A",
  "phone": "0912345678",
  "email": {
    "primary": "<EMAIL>",
    "secondary": ""
  },
  "address": "123 Đường ABC, Quận 1, TP.HCM",
  "avatarFile": {
    "fileName": "avatar.jpg",
    "mimeType": "image/jpeg"
  }
}
```

### 4. Backend Response với Upload URL
```json
{
  "success": true,
  "data": {
    "customer": {
      "id": 1,
      "name": "Nguyễn Văn A",
      "email": "<EMAIL>",
      // ... other customer data
    },
    "avatarUploadUrl": "https://s3.amazonaws.com/bucket/presigned-url-for-avatar-upload"
  }
}
```

### 5. Frontend upload file lên cloud
```typescript
// Trong useUpdateCustomerBasicInfo hook
if (avatarFile && response.data.avatarUploadUrl) {
  await uploadToUrl({
    file: avatarFile,
    presignedUrl: response.data.avatarUploadUrl,
    onUploadProgress: (progress) => {
      onAvatarUploadProgress(progress); // Update UI progress
    }
  });
}
```

## UI Features

### 1. **Avatar Preview**
- Hiển thị preview ngay khi chọn file
- Fallback to default avatar nếu không có

### 2. **Upload Indicators**
- 🔵 Blue upload icon khi có file mới chưa save
- 📊 Progress bar khi đang upload
- 📝 Progress text: "Đang tải lên avatar: 45%"

### 3. **File Validation**
- ✅ Chỉ cho phép file ảnh (image/*)
- ✅ Max size 5MB
- ✅ Error notifications với NotificationUtil

### 4. **User Actions**
- 📤 "Thay đổi" button để chọn file mới
- 🗑️ "Xóa" button để remove avatar
- 💾 "Save" button để lưu tất cả thông tin

### 5. **Loading States**
- Loading spinner khi đang xử lý file
- Disabled buttons khi đang save
- Progress tracking cho upload

## Technical Implementation

### 1. **State Management**
```typescript
const [avatarFile, setAvatarFile] = useState<File | null>(null);
const [avatarUploadProgress, setAvatarUploadProgress] = useState<number>(0);
const [isUploadingAvatar, setIsUploadingAvatar] = useState(false);
```

### 2. **Hook Integration**
```typescript
const updateBasicInfoMutation = useUpdateCustomerBasicInfo({
  onAvatarUploadProgress: (progress) => {
    setAvatarUploadProgress(progress);
  }
});
```

### 3. **File Upload Service**
- Sử dụng `useSimpleFileUpload` hook
- Upload với presigned URL từ backend
- Progress tracking với XMLHttpRequest

### 4. **Error Handling**
- File validation errors → NotificationUtil.error()
- Upload errors → Console log (không fail toàn bộ mutation)
- API errors → Standard error handling

## Backend Requirements

Backend cần implement:

1. **Accept avatar metadata** trong request body
2. **Generate presigned URL** cho S3/cloud storage
3. **Return upload URL** trong response
4. **Handle file upload completion** (optional webhook)

## Testing

### Test Cases:

1. **✅ Chọn file ảnh hợp lệ**
   - Preview hiển thị ngay
   - Upload icon xuất hiện
   - File name hiển thị

2. **✅ Validation errors**
   - File không phải ảnh → error notification
   - File > 5MB → error notification

3. **✅ Upload flow**
   - Click Save → API call với metadata
   - Backend trả về uploadUrl → upload file
   - Progress bar hiển thị 0-100%
   - Success → clear file state

4. **✅ Remove avatar**
   - Click trash icon → clear preview và file
   - Reset progress về 0

## Benefits

1. **🚀 Performance**: Upload trực tiếp lên cloud, không qua server
2. **📊 UX**: Real-time progress tracking
3. **🔒 Security**: Presigned URLs có thời hạn
4. **⚡ Scalability**: Không tải server với file uploads
5. **🎯 Reliability**: Separate upload step, không fail toàn bộ form nếu upload lỗi
